{"name": "playwright_automated_tests", "version": "1.0.0", "license": "MIT", "engines": {"node": ">=22.14.0"}, "description": "Automated test framework using Playwright, which is designed for Web, API and Electron Apps. Stable and Robust layer on top of Playwright with inbuilt Utilities, Linting, Logger, Reports and much more", "keywords": ["playwright", "typescript", "testing", "automationtest"], "dependencies": {"@alex_neo/playwright-azure-reporter": "^1.13.1", "@playwright/test": "^1.55.0", "@types/node": "^22.18.0", "axios": "^1.11.0", "csv-parse": "^6.1.0", "dotenv": "^17.2.1", "exceljs": "^4.4.1-prerelease.0", "fast-xml-parser": "^5.2.5", "https-proxy-agent": "^7.0.6", "js-yaml": "^4.1.0", "mammoth": "^1.10.0", "monocart-reporter": "^2.9.21", "tsconfig-paths": "^4.2.0", "tslib": "^2.8.1", "winston": "^3.17.0"}, "scripts": {"postinstall": "npx playwright install", "test": "echo $NODE_ENV && npx playwright test", "test:qa": "cross-env NODE_ENV=qa npm run test", "test:prod": "cross-env NODE_ENV=prod npm run test", "test:gui": "npx playwright test --headed --project=chromium --project=firefox", "test:debug": "cross-env NODE_ENV=sit npx playwright test --headed --project=chromium --grep=@debug", "test:chromium": "npx playwright test --project=chromium", "test:chromium:headed": "npx playwright test --headed --project=chromium", "test:firefox": "npx playwright test --project=firefox", "test:firefox:headed": "npx playwright test --headed --project=firefox", "test:match": "npx playwright test", "lint": "npx eslint ."}, "devDependencies": {"@eslint/js": "^9.23.0", "@types/csv-parse": "^1.1.12", "@types/js-yaml": "^4.0.9", "cross-env": "^10.0.0", "eslint": "^9.23.0", "eslint-plugin-playwright": "^2.2.2", "globals": "^16.3.0", "httpntlm": "^1.8.13", "jiti": "^2.5.1", "ts-node": "^10.9.2", "typescript": "^5.9.2", "typescript-eslint": "^8.41.0"}, "overrides": {"glob": "^11.0.0", "rimraf": "^6.0.1", "uuid": "^10.0.0", "lodash.isequal": "npm:fast-deep-equal@^3.1.3", "fstream": "npm:tar@^6.2.1", "q": "npm:es6-promisify@^7.0.0"}}