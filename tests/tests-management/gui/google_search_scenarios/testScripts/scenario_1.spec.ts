import { test as baseTest, test } from '@pageobjects/pageFixture';
import { pageUrl ,searchKeyword} from '@testpreparation/testSetup'

baseTest.describe("Test Post Status", () => {
    test("Verify Post Status", async ({ serviceDashboard }) => {
        await serviceDashboard.homePage.openGoogle(pageUrl as string);
        await serviceDashboard.searchComponent.searchFor(searchKeyword as string);
    });
});