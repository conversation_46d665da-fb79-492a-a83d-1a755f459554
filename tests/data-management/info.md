# Data Management for Playwright Automation Tests

## Introduction

The **Data Management** section of the project is responsible for handling all test data, including mapping data models and defining helper functions for interacting with test data. This ensures consistency and easy access to test data across various test scenarios, especially for APIs and UI testing.

In this section, you will find:

- **Data Models:** Definitions of the structure of the data used in tests, which should be stored in sub folder `models`
- **Data Mapping Helpers:** Functions to map, transform, or manipulate test data as needed, , which should be stored in sub folder `handlers`