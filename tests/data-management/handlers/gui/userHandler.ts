import fs from 'fs';
import path from 'path';
import { UserModel } from '@datamanagement/models/gui/userModel';

type CredentialsJson = {
  [env: string]: {
    [application: string]: {
      [role: string]: {
        adUsername?: string;
        'ad-username'?: string;
        username: string;
        password: string;
      };
    };
  };
};

export function getUserData(credentialsPath: string, env: string): UserModel {
  const absolutePath = path.resolve(credentialsPath);
  const rawData = fs.readFileSync(absolutePath, 'utf-8');

  const parsedData = JSON.parse(rawData) as CredentialsJson;

  const envData = parsedData[env];
  if (!envData) {
    throw new Error(`Environment '${env}' not found in ${credentialsPath}`);
  }

  const userModel: UserModel = {};

  for (const [application, roles] of Object.entries(envData)) {
    userModel[application] = {};

    for (const [role, user] of Object.entries(roles)) {
      userModel[application][role] = {
        adUsername: user.adUsername || user['ad-username'] || '',
        username: user.username,
        password: user.password,
        application,
      };
    }
  }

  return userModel;
}