import { test as baseTest } from '@playwright/test';
import { pageMap, createPageObject } from '@utilities/ui/page-factory';
import { ServiceDashboard } from './pageObjectsManagement';


/**
 * A hook that runs after each test, deleting the page context.
 */
// baseTest.afterEach(() => {
//   const workerIndex = baseTest.info().workerIndex;
//   console.log(`[page-factory] Cleaning up page for worker ${workerIndex}`);
//   pageMap.delete(workerIndex);
// });

baseTest.afterEach(async ({}, testInfo) => {
  const workerIndex = testInfo.workerIndex;
  console.log(`[page-factory] Cleaning up page for worker ${workerIndex}`);
  pageMap.delete(workerIndex);
});

export type PageObjectServices = {
  serviceDashboard: ServiceDashboard;
};

// Extend the baseTest from the test framework to include page objects.
// The baseTest object is extended to ensure these page objects are available in the test setup.
export const test = baseTest.extend<PageObjectServices>({
  serviceDashboard: createPageObject(ServiceDashboard),
});

export { expect, Page, Locator} from "@playwright/test";
