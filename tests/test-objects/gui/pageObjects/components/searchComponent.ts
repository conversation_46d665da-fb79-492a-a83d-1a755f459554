import { type Locator } from "@playwright/test";
import * as pageActions from '@utilities/ui/page-actions';

export default class SearchComponent {
    private searchInput: Locator=pageActions.getLocator("//textarea[@aria-label='Search']");
    private submitButton: Locator=pageActions.getLocator("//input[@aria-label='Google Search']");
    async searchFor(keyword: string) {
        await pageActions.fillAndEnter(this.searchInput, keyword);
    }
}
