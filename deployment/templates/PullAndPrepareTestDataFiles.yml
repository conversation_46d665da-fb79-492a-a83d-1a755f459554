parameters:
  - name: ApplicationName
    type: string
  - name: TestEnvironment
    type: string

steps:
  # 1. Download secure file
  - task: DownloadSecureFile@1
    name: downloadCredentials
    inputs:
      secureFile: ${{ parameters.ApplicationName }}-credentials.zip
    displayName: 'Download credentials ZIP archive'

  # 2. Extract .zip to a temp folder
  - task: ExtractFiles@1
    inputs:
      archiveFilePatterns: '$(downloadCredentials.secureFilePath)'
      destinationFolder: '$(Agent.TempDirectory)\credentials'
      cleanDestinationFolder: true
    displayName: 'Extract credentials ZIP'

  # 3. Copy files to Playwright test-data folder
  - powershell: |
      $sourcePath = "$(Agent.TempDirectory)\credentials"
      $destPath = "$(System.DefaultWorkingDirectory)\tests\test-data\user-data\${{ parameters.ApplicationName }}"
      Write-Host "Source: $sourcePath"
      Write-Host "Destination: $destPath"
      New-Item -ItemType Directory -Path $destPath -Force | Out-Null
      Copy-Item -Path "$sourcePath\*" -Destination $destPath -Recurse -Force
    displayName: 'Copy extracted files to test-data folder'

  # 4. Get and Write .env values
  - powershell: |
      $envDir = ".\environments"
      if (-not (Test-Path $envDir)) {
        Write-Host "Creating directory $envDir"
        New-Item -ItemType Directory -Path $envDir | Out-Null
      }
      $envPath = Join-Path $envDir "${{ parameters.TestEnvironment }}.env"
      Write-Host "Writing environment file to: $envPath"
      
      # Use variables passed in environment explicitly
      $apiAuthToken = $Env:API_AUTH_TOKEN
      $apiBaseUrl = $Env:API_BASE_URL
      $baseUrl = $Env:BASE_URL
      $projectName = $Env:PROJECT_NAME

      Set-Content -Path $envPath -Value "API_AUTH_TOKEN=$apiAuthToken" -Encoding utf8
      Add-Content -Path $envPath -Value "API_BASE_URL=$apiBaseUrl"
      Add-Content -Path $envPath -Value "BASE_URL=$baseUrl"
      Add-Content -Path $envPath -Value "PROJECT_NAME=$projectName"
    displayName: 'Write .env'
    env:
      API_AUTH_TOKEN: $(API_AUTH_TOKEN)
      API_BASE_URL: $(API_BASE_URL)
      BASE_URL: $(BASE_URL)
      PROJECT_NAME: $(PROJECT_NAME)

  - powershell: |
      $envPath = ".\environments\${{ parameters.TestEnvironment }}.env"
      if (Test-Path $envPath) {
        Write-Host "Contents of ${envPath}:"
        Get-Content $envPath | ForEach-Object { Write-Host $_ }
      } else {
        Write-Host "File $envPath does not exist."
      }
    displayName: 'Debug: Read and output .env file'

