parameters:
  - name: ApplicationName
    type: string
  - name: TestEnvironment
    type: string

stages:
  - stage: PerformAutomationTest
    displayName: "Perform Automation Test"
    variables:
      - group: ${{ format('{0}-autotest-{1}-buildconfig', parameters.ApplicationName, parameters.TestEnvironment) }}
      - group: ${{ format('{0}-autotest-{1}-env', parameters.ApplicationName, parameters.TestEnvironment) }}
    jobs:
      - job: RunTests
        displayName: "Run Automation Test"
        steps:
          - template: PullAndPrepareTestDataFiles.yml
            parameters:
              ApplicationName: ${{ parameters.ApplicationName }}
              TestEnvironment: ${{ parameters.TestEnvironment }}
          - powershell: |
              Write-Host "Listing files in the source directory (recursive):"
              Get-ChildItem -Path . -Recurse | ForEach-Object { Write-Host $_.FullName }
            displayName: 'Debug: List all files in source directory'

          - task: NodeTool@0
            inputs:
              versionSpec: '22.14.0'
            displayName: 'Install Node.js'

          - script: npm ci
            displayName: 'Install dependencies'

          - script: |
              echo "Running Chromium tests for application: ${{ parameters.ApplicationName }}"
              npx playwright test --project=chromium "tests/tests-management/gui/${{ parameters.ApplicationName }}/"
            displayName: 'Run GUI Playwright Tests - ${{ parameters.TestEnvironment }}'
            env:
              CI: 'true'
              AZURE_TOKEN: $(playwright-azure-reporter-token)
              AZURE_ORG_URL: $(playwright-azure-reporter-orgUrl)
              AZURE_PLAN_ID: $(testplan-id)
              AZURE_ENVIRONMENT: $(environment)
              AZURE_CONFIG_IDS: $(testrun-configurationIds)
              NODE_ENV: ${{ parameters.TestEnvironment }}

          - task: PublishBuildArtifacts@1
            condition: succeededOrFailed()
            inputs:
              PathtoPublish: 'test-results/monocart-report'
              ArtifactName: 'MonocartReport'
              publishLocation: 'Container'
            displayName: 'Publish Monocart HTML Report'

          - task: PublishTestResults@2
            condition: succeededOrFailed()
            inputs:
              testResultsFormat: 'JUnit'
              testResultsFiles: '**/results.xml'
              searchFolder: '$(System.DefaultWorkingDirectory)/test-results'
              testRunTitle: 'Playwright GUI Tests - ${{ parameters.ApplicationName }} - ${{ parameters.TestEnvironment }}'
            displayName: 'Publish Playwright JUnit Results'
