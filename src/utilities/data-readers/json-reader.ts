import fs from 'fs';
import path from 'path';

/**
 * Reads a JSON file and parses it into an object or array.
 *
 * @param fileName - Path to the JSON file (relative or absolute).
 * @returns Parsed JSON content.
 * @throws If the file doesn't exist or contains invalid JSON.
 */
export function readTestDataFromJson(fileName: string): any {
  const filePath = path.resolve(fileName);

  if (!fs.existsSync(filePath)) {
    throw new Error(`JSON file not found: ${filePath}`);
  }

  const rawData = fs.readFileSync(filePath, 'utf-8');

  try {
    return JSON.parse(rawData);
  } catch (err) {
    throw new Error(`Invalid JSON in file: ${filePath}\n${err}`);
  }
}

/**
 * Reads and filters JSON data based on a predicate function.
 *
 * @param fileName - Path to the JSON file.
 * @param filterFn - Function that filters the JSON array.
 * @returns Filtered array of data.
 */
export function readFiltered<PERSON>son(fileName: string, filterFn: (item: any) => boolean): any[] {
  const data = readTestDataFromJson(fileName);

  if (!Array.isArray(data)) {
    throw new Error(`Expected JSON array in file: ${fileName}`);
  }

  return data.filter(filterFn);
}
