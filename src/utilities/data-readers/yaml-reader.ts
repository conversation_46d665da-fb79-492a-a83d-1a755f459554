import * as fs from 'fs';         
import * as yaml from 'js-yaml';  

/**
 * Reads a YAML file and parses its content into a JavaScript object.
 * 
 * @param {string} filePath - The path to the YAML file.
 * @returns {any} The parsed content of the YAML file as a JavaScript object.
 * @throws Will throw an error if the file cannot be read or parsed.
 */
export function readYamlFile(filePath: string): any {
  try {
    const fileContents = fs.readFileSync(filePath, 'utf8');
    const data = yaml.load(fileContents);

    return data;
  } catch (err) {
    console.error(`Failed to read or parse YAML file: ${filePath}`, err);
    throw err;
  }
}

/**
 * Reads a YAML file asynchronously and parses its content into a JavaScript object.
 * @param filePath - The path to the YAML file.
 * @returns Promise resolving to the parsed object.
 * @throws Throws if reading or parsing fails.
 */
export async function readYamlFileAsync(filePath: string): Promise<any> {
    try {
      const fileContents = await fs.promises.readFile(filePath, 'utf8');
      return yaml.load(fileContents);
    } catch (err) {
      console.error(`Failed to asynchronously read or parse YAML file: ${filePath}`, err);
      throw err;
    }
  }

  /**
 * Validates whether the given file exists and is a YAML file.
 * @param filePath - The path to the file to validate.
 * @returns True if the file exists and ends with .yaml or .yml extension.
 */
export function isValidYamlFile(filePath: string): boolean {
    try {
      const exists = fs.existsSync(filePath);
      const isYamlExt = filePath.endsWith('.yaml') || filePath.endsWith('.yml');
      return exists && isYamlExt;
    } catch {
      return false;
    }
  }

/**
 * Parses a YAML string content and returns a JavaScript object.
 * @param yamlString - The YAML content as a string.
 * @returns The parsed object.
 * @throws Throws if parsing fails.
 */
export function parseYamlString(yamlString: string): any {
    try {
      return yaml.load(yamlString);
    } catch (err) {
      console.error(`Failed to parse YAML string`, err);
      throw err;
    }
  }
  