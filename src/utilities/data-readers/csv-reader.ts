import fs from 'fs';
import path from 'path';
import { parse } from 'csv-parse/sync';

/**
 * Reads and parses CSV test data with customizable delimiter.
 *
 * @param fileName - Path to the CSV file.
 * @param delimiter - Optional delimiter (default is comma).
 * @returns Array of records as objects.
 */
export function readTestDataFromCsv(
  fileName: string,
  delimiter: ',' | ';' | '|' = ','
): Record<string, string>[] {
  const filePath = path.resolve(fileName);

  if (!fs.existsSync(filePath)) {
    throw new Error(`CSV file not found: ${filePath}`);
  }

  const fileContent = fs.readFileSync(filePath, 'utf-8');

  try {
    return parse(fileContent, {
      columns: true,
      skip_empty_lines: true,
      trim: true,
      delimiter, // allow flexible delimiter
    });
  } catch (err) {
    throw new Error(`Error parsing CSV file ${filePath} with delimiter "${delimiter}"\n${err}`);
  }
}
