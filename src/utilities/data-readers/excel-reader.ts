import * as XLSX from 'xlsx';
import path from 'path';

/**
 * Reads test data from an Excel file and returns it as an array of objects.
 * 
 * This function assumes the Excel file is structured with:
 * - One sheet (or defaults to the first sheet if multiple)
 * - A header row in the first row, where each column header becomes the key
 * - Data rows below, each row converted to an object
 * 
 * @param {string} fileName - Name of the Excel file, relative to the project root or resolved path
 * @param {string} sheetName - Optional name of the sheet to read; if not provided, the first sheet is used
 * @returns {any[]} Array of test data objects from the sheet
 * 
 * @example
 * const testData = readTestDataFromExcel('./test-data/my-tests.xlsx');
 * testData.forEach(data => {
 *   console.log(data.email, data.password);
 * });
 */
export function readTestDataFromExcel(fileName: string, sheetName?: string): any[] {
  const filePath = path.resolve(fileName);
  const workbook = XLSX.readFile(filePath);
  const actualSheetName = sheetName || workbook.SheetNames[0];
  const worksheet = workbook.Sheets[actualSheetName];

  if (!worksheet) {
    throw new Error(`Sheet "${actualSheetName}" not found in "${fileName}"`);
  }
  const jsonData: any[] = XLSX.utils.sheet_to_json(worksheet);

  return jsonData;
}

/**
 * Returns all available sheet names in the Excel file.
 *
 * @param fileName - Path to the Excel file.
 * @returns Array of sheet names.
 */
export function getSheetNames(fileName: string): string[] {
 const workbook = XLSX.readFile(path.resolve(fileName));
 return workbook.SheetNames;
}

/**
* Reads a specific cell value (like 'A1', 'B2') from a given sheet in an Excel file.
*
* @param fileName - Path to the Excel file.
* @param sheetName - Sheet from which to read the value.
* @param cellAddress - Cell address in A1 notation (e.g., 'B3').
* @returns Value stored in the cell, or undefined if not found.
*/
export function readCellValue(fileName: string, sheetName: string, cellAddress: string): any {
 const workbook = XLSX.readFile(path.resolve(fileName));
 const worksheet = workbook.Sheets[sheetName];
 if (!worksheet) throw new Error(`Sheet "${sheetName}" not found.`);
 return worksheet[cellAddress]?.v; // Return the cell's value
}

/**
* Reads and filters test data from a sheet based on a provided condition.
*
* @param fileName - Path to the Excel file.
* @param sheetName - Sheet to read from.
* @param filter - A function that returns true for rows to include.
* @returns Filtered array of row data.
*/
export function readFilteredData(fileName: string, sheetName: string, filter: (row: any) => boolean): any[] {
 const data = readTestDataFromExcel(fileName, sheetName);
 return data.filter(filter); // Return only rows that match the filter condition
}

/**
* Returns the range of cells that contain data in a sheet (e.g., 'A1:D10').
*
* @param fileName - Path to the Excel file.
* @param sheetName - Optional sheet name.
* @returns Cell range string, or empty string if not found.
*/
export function getSheetRange(fileName: string, sheetName?: string): string {
 const workbook = XLSX.readFile(path.resolve(fileName));
 const sheet = workbook.Sheets[sheetName || workbook.SheetNames[0]];
 return sheet['!ref'] || ''; // '!ref' contains the range info
}
