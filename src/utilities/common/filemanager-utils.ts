import { promises as fs } from 'fs';
import path from 'path';
import * as mammoth from 'mammoth';
import * as ExcelJS from 'exceljs';
import { XMLParser, XMLBuilder } from 'fast-xml-parser';

export default class FileManager {
  /**
   * Reads a file and returns its content as a string.
   * @param filePath - The path of the file to read.
   */
  static async readFile(filePath: string): Promise<string> {
    try {
      return await fs.readFile(filePath, 'utf-8');
    } catch (error) {
      throw new Error(`Error reading file: ${error}`);
    }
  };

  /**
   * Writes data to a file, replacing its content.
   * @param filePath - The path of the file to write.
   * @param data - The content to write to the file.
   */
  static async writeFile(filePath: string, data: string): Promise<void> {
    try {
      await fs.writeFile(filePath, data, 'utf-8');
    } catch (error) {
      throw new Error(`Error writing file: ${error}`);
    }
  };

  /**
   * Appends text to a file.
   * @param filePath - The path of the file to append.
   * @param data - The text to append.
   */
  static async appendToFile(filePath: string, data: string): Promise<void> {
    try {
      await fs.appendFile(filePath, data, 'utf-8');
    } catch (error) {
      throw new Error(`Error appending to file: ${error}`);
    }
  };

  /**
   * Deletes a file.
   * @param filePath - The path of the file to delete.
   */
  static async deleteFile(filePath: string): Promise<void> {
    try {
      await fs.unlink(filePath);
    } catch (error) {
      throw new Error(`Error deleting file: ${error}`);
    }
  };

  /**
   * Returns the file extension.
   * @param filePath - The path of the file.
   */
  static getFileExtension(filePath: string): string {
    return path.extname(filePath).toLowerCase();
  };

  /**
   * Returns the file name without extension.
   * @param filePath - The path of the file.
   */
  static getFileName(filePath: string): string {
    return path.basename(filePath, path.extname(filePath));
  };

  /**
   * Reads a DOCX file and extracts text.
   * @param filePath - The path of the DOCX file.
   */
  static async readDocxFile(filePath: string): Promise<string> {
    if (!(await this.fileExists(filePath))) {
      throw new Error(`DOCX file not found: ${filePath}`);
    }
    try {
      const buffer = await fs.readFile(filePath);
      const result = await mammoth.extractRawText({ buffer });
      return result.value;
    } catch (error) {
      throw new Error(`Error reading DOCX file: ${error}`);
    }
  };

  /**
   * Reads an XLSX file and returns JSON data.
   * @param filePath - The path of the XLSX file.
   */
  static async readXlsxFile(filePath: string): Promise<any[]> {
    if (!(await this.fileExists(filePath))) {
      throw new Error(`XLSX file not found: ${filePath}`);
    }
    try {
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.readFile(filePath);
      const worksheet = workbook.worksheets[0];
      return worksheet.getSheetValues();
    } catch (error) {
      throw new Error(`Error reading XLSX file: ${error}`);
    }
  };

  /**
   * Writes JSON data to an XLSX file.
   * @param filePath - The path of the XLSX file.
   * @param data - The JSON data to write.
   */
  static async writeXlsxFile(filePath: string, data: any[]): Promise<void> {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Sheet1');
      worksheet.addRows(data);
      await workbook.xlsx.writeFile(filePath);
    } catch (error) {
      throw new Error(`Error writing XLSX file: ${error}`);
    }
  };

  /**
   * Reads an XML file and returns JSON data.
   * @param filePath - The path of the XML file.
   */
  static async readXmlFile(filePath: string): Promise<any> {
    if (!(await this.fileExists(filePath))) {
      throw new Error(`XML file not found: ${filePath}`);
    }
    try {
      const data = await fs.readFile(filePath, 'utf-8');
      const parser = new XMLParser();
      return parser.parse(data);
    } catch (error) {
      throw new Error(`Error reading XML file: ${error}`);
    }
  };

  /**
   * Writes JSON data to an XML file.
   * @param filePath - The path of the XML file.
   * @param data - The JSON data to convert and write.
   */
  static async writeXmlFile(filePath: string, data: any): Promise<void> {
    try {
      const builder = new XMLBuilder();
      const xmlData = builder.build(data);
      await fs.writeFile(filePath, xmlData, 'utf-8');
    } catch (error) {
      throw new Error(`Error writing XML file: ${error}`);
    }
  };

  /**
   * Checks if a file exists.
   * @param filePath - The path of the file.
   */
  static async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  };
}
