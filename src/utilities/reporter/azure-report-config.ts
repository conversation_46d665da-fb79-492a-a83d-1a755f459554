export const AZURE_REPORTER_CONFIG = {
    orgUrl: process.env.AZURE_ORG_URL || '',
    token: process.env.AZURE_TOKEN || '',
    planId: Number(process.env.AZURE_PLAN_ID) || 1,
    projectName: '', 
    environment: process.env.AZURE_ENVIRONMENT || 'dev',
    logging: true,
    testRunTitle: 'Playwright Test Run',
    publishTestResultsMode: 'testRun',
    uploadAttachments: true,
    attachmentsType: ['screenshot', 'trace', 'html'],
    uploadPassedTestAttachments: true,
    testCaseIdMatcher: /@\[(\d+)\]/,
    testRunConfig: {
      comment: 'Playwright Test Run',
      configurationIds: [Number(process.env.AZURE_CONFIG_IDS || '1')],
    },
  };