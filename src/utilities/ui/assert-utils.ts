/**
 * assert-utils.ts: This module contains utility functions for assertions in tests.
 * All expect assertions will dynamically wait until either the expect timeout specified in the
 * playwright.config is reached or the condition becomes true.
 * @module AssertUtils
 */

import { Expect, Locator, TestInfo, expect } from '@playwright/test';
import { ExpectOptions, ExpectTextOptions, SoftOption } from './parameter-types';
import { getLocator, getAllLocators } from './page-actions';
import { getPage } from './page-factory';
import { logger } from '@utilities/reporter/custom-logger';

/**
 * Type guard to check if an object is a Playwright Locator.
 */
function isLocator(obj: any): obj is Locator {
  return obj && typeof obj === 'object' && typeof obj['click'] === 'function';
}

/**
 * Returns an Expect object configured with the given soft option.
 * @param {SoftOption} options - The soft option to configure the Expect object with.
 * @returns {Expect} - The configured Expect object.
 */
function getExpectWithSoftOption(options?: SoftOption): Expect {
  return expect.configure({ soft: options?.soft });
}

/**
 * Returns a Locator object and an Expect object configured with the given soft option.
 * @param {string | Locator} input - Either a string (selector) or a Locator object.
 * @param {SoftOption} options - The soft option to configure the Expect object with.
 * @returns {Object} - An object containing the Locator and Expect objects.
 */
function getLocatorAndAssert(input: string | Locator, options?: SoftOption): { locator: Locator; assert: Expect } {
  const locator = getLocator(input);
  const assert = getExpectWithSoftOption(options);
  return { locator, assert };
}

/**
 * Use this function to assert all the soft assertions.
 * @param {TestInfo} testInfo - The TestInfo object containing the test's information.
 */
export function assertAllSoftAssertions(testInfo: TestInfo) {
  expect(testInfo.errors).toHaveLength(0);
}

/**
 * 1. Locator Assertions: This section contains functions that perform assertions on specific locators.
 * These functions check for various conditions such as visibility, presence in the DOM, text content, etc.
 */

/**
 * Asserts that the given element is not present in the DOM or is Hidden.
 * @param {string | Locator} input - Either a string (selector) or a Locator object.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectElementToBeHidden(input: string | Locator, options?: ExpectOptions): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).toBeHidden(options);
  logger.info(`[LocatorAssertions] Verify Element to be Hidden - ${locator} is hidden as expected`);
}

/**
 * Asserts that the given element is present in the DOM and visible.
 * @param {string | Locator} input - Either a string (selector) or a Locator object.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectElementToBeVisible(input: string | Locator, options?: ExpectOptions): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).toBeVisible(options);
  logger.info(`[LocatorAssertions] Verify Element to be Visible - ${locator} is Visbile as expected`);
}

/**
 * Asserts that all provided elements are visible in the DOM.
 * Supports input as a selector string, a Locator, or a Promise resolving to Locator[].
 * @param input - A string selector, a Locator, or a Promise resolving to Locator[].
 * @param options - Optional Playwright ExpectOptions.
 */
export async function expectAllElementsToBeVisible(
  input: string | Locator | Promise<Locator[]>,
  options?: ExpectOptions
): Promise<void> {
  let locators: Locator[];

  if (input instanceof Promise) {
    locators = await input;
  } else if (typeof input === 'string' || isLocator(input)) {
    locators = await getAllLocators(input); 
  } else {
    throw new Error(`[LocatorAssertions] Unsupported input type: ${typeof input}`);
  }

  if (locators.length === 0) {
    throw new Error(`[LocatorAssertions] No elements found for input: ${input}`);
  }

  for (const locator of locators) {
    await expect(locator).toBeVisible(options);
    logger?.info?.(`[LocatorAssertions] Element is visible: ${await locator.evaluate(el => el.outerHTML)}`);
  }
}

/**
 * Asserts that the given element is present in the DOM.
 * @param {string | Locator} input - Either a string (selector) or a Locator object.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectElementToBeAttached(input: string | Locator, options?: ExpectOptions): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).toBeAttached(options);
  logger.info(`[LocatorAssertions] Verify Element to be Attached - ${locator} is attached as expected`);
}

/**
 * Asserts that the given element is present in the DOM and visible in the viewport of the page.
 * @param {string | Locator} input - Either a string (selector) or a Locator object.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectElementToBeInViewport(input: string | Locator, options?: ExpectOptions): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).toBeInViewport(options);
  logger.info(`[LocatorAssertions] Verify Element to be In View port - ${locator} is available in View port as expected`);
}

/**
 * Asserts that the given element is checked.
 * @param {string | Locator} input - Either a string (selector) or a Locator object.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectElementToBeChecked(input: string | Locator, options?: ExpectOptions): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).toBeChecked(options);
  logger.info(`[LocatorAssertions] Verify Element to be checked - ${locator} is checked as expected`);
}

/**
 * Asserts that the given element is not checked.
 * @param {string | Locator} input - Either a string (selector) or a Locator object.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectElementNotToBeChecked(input: string | Locator, options?: ExpectOptions): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).not.toBeChecked(options);
  logger.info(`[LocatorAssertions] Verify Element not be checked - ${locator} is not checked as expected`);
}

/**
 * Asserts that the given element is disabled.
 * @param {string | Locator} input - Either a string (selector) or a Locator object.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectElementToBeDisabled(input: string | Locator, options?: ExpectOptions): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).toBeDisabled(options);
  logger.info(`[LocatorAssertions] Verify Element to be disabled - ${locator} is disabled as expected`);
}

/**
 * Asserts that the given element is enabled.
 * @param {string | Locator} input - Either a string (selector) or a Locator object.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectElementToBeEnabled(input: string | Locator, options?: ExpectOptions): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).toBeEnabled(options);
  logger.info(`[LocatorAssertions] Verify Element to be enabled - ${locator} is enabled as expected`);
}

/**
 * Asserts that the given element is editable.
 * @param {string | Locator} input - Either a string (selector) or a Locator object.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectElementToBeEditable(input: string | Locator, options?: ExpectOptions): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).toBeEditable(options);
  logger.info(`[LocatorAssertions] Verify Element to be editatble - ${locator} is Editable as expected`);
}

/**
 * Asserts that the element equals the provided string or string array or regular expression.
 * @param {string | Locator} input - Either a string (selector) or a Locator object from where we retrieve the text to assert.
 * @param {string | string[] | RegExp} text - The string, string array or regular expression to match against the element's text.
 * @param {ExpectOptions & ExpectTextOptions} options - The options to pass to the expect function.
 */
export async function expectElementToHaveText(
  input: string | Locator,
  text: string | RegExp | Array<string | RegExp>,
  options?: ExpectOptions & ExpectTextOptions,
): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).toHaveText(text, options);
  logger.info(`[LocatorAssertions] Verify Element to be have Text - ${locator} has text value as expected: ${text}`);
}

/**
 * Asserts that the element does not equal the provided string or string array or regular expression.
 * @param {string | Locator} input - Either a string (selector) or a Locator object from where we retrieve the text to assert.
 * @param {string | string[] | RegExp} text - The string, string array or regular expression to match against the element's text.
 * @param {ExpectOptions & ExpectTextOptions} options - The options to pass to the expect function.
 */
export async function expectElementNotToHaveText(
  input: string | Locator,
  text: string | RegExp | Array<string | RegExp>,
  options?: ExpectOptions & ExpectTextOptions,
): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).not.toHaveText(text, options);
  logger.info(`[LocatorAssertions] Verify Element not have Text - ${locator} NOT keeping the text value: ${text}`);
}

/**
 * Asserts that the element contains the provided string or string array or regular expression.
 * @param {string | Locator} input - Either a string (selector) or a Locator object from where we retrieve the text to assert.
 * @param {string | string[] | RegExp} text - The string, string array or regular expression to match against the element's text.
 * @param {ExpectOptions & ExpectTextOptions} options - The options to pass to the expect function.
 */
export async function expectElementToContainText(
  input: string | Locator,
  text: string | RegExp | Array<string | RegExp>,
  options?: ExpectOptions & ExpectTextOptions,
): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).toContainText(text, options);
  logger.info(`[LocatorAssertions] Verify Element is contains the Text - ${locator} has containing: ${text} as expected`);
}

/**
 * Asserts that a given Locator contains all expected text values across its matched elements.
 * Waits for the locator to appear and verifies that all expected values are present.
 *
 * @param {Locator} locator - A Playwright Locator resolving to multiple elements.
 * @param {string[]} expectedValues - Array of expected text strings that should be present in the elements.
 * @param {number} [timeoutMs=5000] - Optional timeout in milliseconds to wait for the locator to appear.
 * @param {boolean} [caseInsensitive=false] - If true, compares texts without considering letter casing.
 *
 * @throws Will throw an error if the locator is empty or any expected value is missing.
 */
export async function expectLocatorToContainAllTexts(locator: Locator, expectedValues: string[], timeoutMs: number = 5000, caseInsensitive: boolean = false) {
  await locator.first().waitFor({ timeout: timeoutMs });

  const elements = await locator.all();
  const actualTexts = await Promise.all(elements.map(el => el.textContent()));
  const actualNormalizedTexts = actualTexts
    .filter((text): text is string => typeof text === 'string') // filters out null/undefined
    .map(text => text.trim())
    .filter(text => text.length > 0); // filters out empty strings

  const normalizedActuals = caseInsensitive
    ? actualNormalizedTexts.map(text => text.toLowerCase())
    : actualNormalizedTexts;

  const normalizedExpecteds = caseInsensitive
    ? expectedValues.map(text => text.toLowerCase())
    : expectedValues;

  for (const expected of normalizedExpecteds) {
    if (!normalizedActuals.includes(expected)) {
      throw new Error(`Missing expected text${caseInsensitive ? ' (case-insensitive)' : ''}: "${expected}"`);
    }
  }

  logger.info(`[LocatorAssertions] All expected texts are present: ${expectedValues.join(', ')}`);
}

/**
 * Asserts that the element does not contain the provided string or string array or regular expression.
 * @param {string | Locator} input - Either a string (selector) or a Locator object from where we retrieve the text to assert.
 * @param {string | string[] | RegExp} text - The string, string array or regular expression to match against the element's text.
 * @param {ExpectOptions & ExpectTextOptions} options - The options to pass to the expect function.
 */
export async function expectElementNotToContainText(
  input: string | Locator,
  text: string | RegExp | Array<string | RegExp>,
  options?: ExpectOptions & ExpectTextOptions,
): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).not.toContainText(text, options);
  logger.info(`[LocatorAssertions] Verify Element does not contains the Text - ${locator} is not containing: ${text}`);
}

/**
 * Asserts that the given element points to an input text box with the given text or Regex.
 * @param {string | Locator} input - Either a string (selector) or a Locator object from where we retrieve the input value to assert.
 * @param {string | RegExp} text - The string or regular expression to match against the element's value.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectElementToHaveValue(
  input: string | Locator,
  text: string | RegExp,
  options?: ExpectOptions,
): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).toHaveValue(text, options);
  logger.info(`[LocatorAssertions] Verify Element has the value - ${locator} has value: ${text} as expected`);
}

/**
 * Asserts that the given element points to a multi-select/combobox (i.e. a select with the multiple attribute) and the specified values are selected.
 * @param {string | Locator} input - Either a string (selector) or a Locator object from where we retrieve the input value to assert.
 * @param {Array<string | RegExp>} text - The array of strings or regular expressions to match against the element's values.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectElementToHaveValues(
  input: string | Locator,
  text: Array<string | RegExp>,
  options?: ExpectOptions,
): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).toHaveValues(text, options);
  logger.info(`[LocatorAssertions] Verify Element is contains the list of Values - ${locator} has containing list values: ${text} as expected`);
}

/**
 * Asserts that the given element points to a multi-select/combobox (i.e. a select with the multiple attribute) and the specified values are selected.
 * @param {string | Locator} input - Either a string (selector) or a Locator object from where we retrieve the input value to assert.
 * @param {Array<string | RegExp>} text - The array of strings or regular expressions to match against the element's values.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectElementsToContainsValues(
  input: string | Locator,
  text: Array<string | RegExp>,
  options?: ExpectOptions,
): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).toContainText(text, options);
  logger.info(`[LocatorAssertions] Verify Element is contains the list of Values - ${locator} has containing list values: ${text} as expected`);
}

/**
 * Asserts that the given element points to an empty editable element or to a DOM node that has no text.
 * @param {string | Locator} input - Either a string (selector) or a Locator object from where we retrieve the input value to assert.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectElementValueToBeEmpty(input: string | Locator, options?: ExpectOptions): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).toBeEmpty(options);
  logger.info(`[LocatorAssertions] Verify Element value is empty - ${locator} value is empty as expected`);
}

/**
 * Asserts that the given element points to a non-empty editable element or to a DOM node that has text.
 * @param {string | Locator} input - Either a string (selector) or a Locator object from where we retrieve the input value to assert.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectElementValueNotToBeEmpty(input: string | Locator, options?: ExpectOptions): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).not.toBeEmpty(options);
  logger.info(`[LocatorAssertions] Verify Element is NOT empty - ${locator} is not empty as epxected`);
}

/**
 * Asserts that an element has an attribute with the given value.
 * @param {string | Locator} input - Either a string (selector) or a Locator object.
 * @param {string} attribute - The attribute to check for.
 * @param {string | RegExp} value - The value to match against the attribute.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectElementToHaveAttribute(
  input: string | Locator,
  attribute: string,
  value: string | RegExp,
  options?: ExpectOptions,
): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).toHaveAttribute(attribute, value, options);
  logger.info(`[LocatorAssertions] Verify Element Attribute value - ${locator} has attribute ${attribute} with value ${value} as expected`);
}

/**
 * Asserts that an element has an attribute which contains the given value.
 * @param {string | Locator} input - Either a string (selector) or a Locator object.
 * @param {string} attribute - The attribute to check for.
 * @param {string | RegExp} value - The value to match against the attribute.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectElementToContainAttribute(
  input: string | Locator,
  attribute: string,
  value: string | RegExp,
  options?: ExpectOptions,
): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).toHaveAttribute(attribute, new RegExp(value), options);
  logger.info(`[LocatorAssertions] Verify Element Attribute value - ${locator} has containing attribute ${attribute} with value ${value} as expected`);
}

/**
 * Asserts that the given element has the specified count.
 * @param {string | Locator} input - Either a string (selector) or a Locator object to get the element count.
 * @param {number} count - The count to match against the element's count.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectElementToHaveCount(
  input: string | Locator,
  count: number,
  options?: ExpectOptions,
): Promise<void> {
  const { locator, assert } = getLocatorAndAssert(input, options);
  await assert(locator, options).toHaveCount(count, options);
  logger.info(`[ElementAssertion] Verify Number of Elements - ${locator} has totally ${count} Element`);
}

/**
 * 2. Page Assertions: This section contains functions that perform assertions on the entire page.
 * These functions check for conditions such as URL, title, etc.
 */

/**
 * Asserts that the current page URL matches exactly the provided URL or regular expression.
 * @param {string | RegExp} urlOrRegExp - The URL or regular expression to match against the current page URL.
 */
export async function expectPageToHaveURL(urlOrRegExp: string | RegExp, options?: ExpectOptions): Promise<void> {
  const assert = getExpectWithSoftOption(options);
  await assert(getPage()).toHaveURL(urlOrRegExp, options);
  logger.info(`[Page Assertions] Current Page url following url ${urlOrRegExp} as expected`);
}

/**
 * Asserts that the current page URL contains the provided URL.
 * @param {string } url - The URL to match against the current page URL.
 */
export async function expectPageToContainURL(url: string, options?: ExpectOptions): Promise<void> {
  const assert = getExpectWithSoftOption(options);
  await assert(getPage()).toHaveURL(new RegExp(url), options);
  logger.info(`[Page Assertions] Page current Url contains ${url} as expected`);
}

/**
 * This method will be used for future stories validations Asserts that the current page Title
 * matches exactly the provided title or regular expression.
 * @param {string | RegExp} titleOrRegExp - The title or regular expression to match against the current page title.
 * @param {ExpectOptions} options - The options to pass to the expect function.
 */
export async function expectPageToHaveTitle(titleOrRegExp: string | RegExp, options?: ExpectOptions): Promise<void> {
  const assert = getExpectWithSoftOption(options);
  await assert(getPage()).toHaveTitle(titleOrRegExp, options);
  logger.info(`[Page Assertions] Current Page url following title ${titleOrRegExp} as expected`);
}