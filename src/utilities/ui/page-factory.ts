/**
 * page-factory.ts: This module is responsible for setting and managing instances of pages.
 * It provides a centralized way to set and access pages, ensuring that each test has a clean, isolated page instance.
 * This helps to maintain the state and context of each test independently, improving test reliability and debugging.
 * It also includes functions for switching between pages, closing pages, and reverting to the default page.
 */

import test, { Page } from '@playwright/test';

export const pageMap = new Map<number, Page>();

export function setPage(pageInstance: Page): void {
  const workerIndex = test.info().workerIndex;
  console.log(`[page-factory] setPage called for worker ${workerIndex}`);
  pageMap.set(workerIndex, pageInstance);
}

export function getPage(): Page {
  const workerIndex = test.info().workerIndex;
  const page = pageMap.get(workerIndex);
  if (!page) throw new Error(`[page-factory] Page not found for worker ${workerIndex}. Make sure setPage() was called.`);
  return page;
}

// `createPageObject` is a generic function that helps create and initialize a page object
// for a given page class. It accepts a `PageClass`, which is a constructor function for the page object,
// and returns a function that interacts with a page instance in the context of a test.
export const createPageObject = <T>(PageClass: new () => T) =>
  // The returned function accepts an object with a `page` property and a `use` function.
  // `page` represents the page instance in the test, and `use` is a callback function that is invoked
  // with the created page object (of type `T`).
  async ({page }: { page: Page }, use: (obj: T) => void) => {
    setPage(page);
    // Create an instance of the `PageClass` by calling its constructor.
    // `PageClass` is a class type, so this creates a new instance of it.
    const pageObject = new PageClass();

    // The `pageObject` is passed to the `use` function, which is a callback provided by the test framework.
    // The `use` function is typically used to pass the page object instance to the test context.
    // It ensures that the test has access to the page object for interacting with the page.
    await use(pageObject);
  };