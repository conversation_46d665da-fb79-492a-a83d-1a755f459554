/**
 * playwright.config.ts: This module is responsible for configuring the Playwright test runner.
 * It includes settings for test execution, browser configuration, and environment variables.
 * See https://playwright.dev/docs/test-configuration for more details.
 */

import { ACTION_TIMEOUT, EXPECT_TIMEOUT, NAVIGATION_TIMEOUT, TEST_TIMEOUT } from '@utilities/ui/timeout-const';
import { defineConfig, ReporterDescription } from '@playwright/test';
import * as dotenv from 'dotenv';
import { PLAYWRIGHT_PROJECT_CONFIG } from './configs/project.config';
import { WaitForLoadStateOptions } from '@utilities/ui/parameter-types';
import { MONOCART_CONFIG } from '@utilities/reporter/monocart-config';
import { AZURE_REPORTER_CONFIG } from '@utilities/reporter/azure-report-config';


switch (process.env.NODE_ENV) {
  case 'qa': dotenv.config({ path: './environments/qa.env' }); break;
  case 'prod': dotenv.config({ path: './environments/prod.env' }); break;
  default: dotenv.config({ path: './environments/qa.env' });
};

/**
 * Default load state to be used while loading a URL or performing a click and navigate operation.
 * The load state is set to 'domcontentloaded', which means the action will wait until the 'DOMContentLoaded' event is fired.
 */
export const LOADSTATE: WaitForLoadStateOptions = 'domcontentloaded';
const isCI = !!process.env.CI;

const reporters: ReporterDescription[] = [
  ['./src/utilities/reporter/custom-logger.ts'],
  ['junit', { outputFile: 'test-results/results.xml' }],
  ['monocart-reporter', MONOCART_CONFIG],
  ['list'],
];

if (process.env.CI) {
  reporters.splice(reporters.length - 1, 0, [
    '@alex_neo/playwright-azure-reporter',
    AZURE_REPORTER_CONFIG as any,
  ]);
}

export default defineConfig({
  testDir: './tests/tests-management',
  outputDir: './test-results/artifacts',
  snapshotPathTemplate: '{testDir}/__screenshots__/{testFilePath}/{arg}{ext}',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 1 : 0,
  workers: process.env.CI ? 4 : 8,
  timeout: TEST_TIMEOUT,
  expect: {
    timeout: EXPECT_TIMEOUT,
  },
  metadata: {
    product: `Playwright Automated Test - ${process.env.PROJECT_NAME}`,
    url: `${process.env.BASE_URL}`,
    env: `${process.env.NODE_ENV}` || 'sit',
  },
  reporter: reporters,
  use: {
    trace: 'off',
    screenshot: 'only-on-failure',
    actionTimeout: ACTION_TIMEOUT,
    navigationTimeout: NAVIGATION_TIMEOUT,
    viewport: null,
    bypassCSP: true,
    launchOptions: {
      args: ["--start-maximized"]
    },
    ignoreHTTPSErrors: true,
    acceptDownloads: true,

  },
  projects: PLAYWRIGHT_PROJECT_CONFIG
});